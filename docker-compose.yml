version: '3.8'

services:
  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - yue-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DEBUG=true
      - MYSQL_SERVER=mysql
      - REDIS_HOST=redis
      - MILVUS_HOST=milvus
      - MINIO_ENDPOINT=minio:9000
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - mysql
      - redis
      - milvus
      - minio
    networks:
      - yue-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=yue_ai_agent
      - MYSQL_USER=yue_user
      - MYSQL_PASSWORD=yue_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - yue-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - yue-network

  # Milvus向量数据库
  etcd:
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - yue-network

  minio:
    image: minio/minio:RELEASE.2023-11-20T22-40-07Z
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - yue-network

  milvus:
    image: milvusdb/milvus:v2.3.4
    ports:
      - "19530:19530"
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    depends_on:
      - etcd
      - minio
    networks:
      - yue-network

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    command: celery -A app.core.celery worker --loglevel=info
    environment:
      - DEBUG=true
      - MYSQL_SERVER=mysql
      - REDIS_HOST=redis
      - MILVUS_HOST=milvus
      - MINIO_ENDPOINT=minio:9000
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    depends_on:
      - mysql
      - redis
      - milvus
      - minio
    networks:
      - yue-network

volumes:
  mysql_data:
  redis_data:
  milvus_data:
  etcd_data:
  minio_data:

networks:
  yue-network:
    driver: bridge
