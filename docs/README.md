# YUE智能体综合应用平台 - 文档中心

欢迎来到YUE智能体综合应用平台的文档中心！这里包含了项目的完整技术文档。

## 📚 文档导航

### 🏗️ 架构设计
- **[系统架构设计](architecture.md)** - 完整的系统技术架构、部署架构和安全架构
- **[智能体架构设计](agents-architecture.md)** - 四大智能体的详细设计和实现方案
- **[项目结构说明](project-structure.md)** - 详细的目录结构和技术栈说明

### 🚀 快速开始
- **[快速启动指南](quick-start.md)** - 项目安装、配置和启动的完整指南
- **[开发环境搭建](development.md)** - 开发环境的详细配置说明
- **[部署指南](deployment.md)** - 生产环境部署的最佳实践

### 🤖 智能体文档
- **[智能客服智能体](agents/customer-service.md)** - 智能客服的功能和API文档
- **[Text2SQL智能体](agents/text2sql.md)** - 数据分析智能体的使用指南
- **[知识库问答智能体](agents/knowledge-base.md)** - 知识库问答的配置和使用
- **[文案创作智能体](agents/content-creation.md)** - 文案创作功能的详细说明

### 🔧 开发指南
- **[API文档](api/README.md)** - 完整的后端API接口文档
- **[前端组件库](frontend/components.md)** - 前端组件的使用说明
- **[数据库设计](database/schema.md)** - 数据库表结构和关系设计
- **[配置管理](configuration.md)** - 环境变量和配置文件说明

### 🛠️ 运维指南
- **[监控和日志](monitoring.md)** - 系统监控和日志管理
- **[性能优化](performance.md)** - 性能调优的最佳实践
- **[故障排除](troubleshooting.md)** - 常见问题和解决方案
- **[备份恢复](backup-recovery.md)** - 数据备份和灾难恢复

## 🎯 快速导航

### 新手入门
1. 📖 阅读 [系统架构设计](architecture.md) 了解整体架构
2. 🚀 按照 [快速启动指南](quick-start.md) 搭建环境
3. 🤖 查看 [智能体架构设计](agents-architecture.md) 了解核心功能
4. 💻 参考 [开发指南](development.md) 开始开发

### 系统管理员
1. 🏗️ 查看 [部署指南](deployment.md) 了解部署方案
2. 📊 配置 [监控和日志](monitoring.md) 系统
3. ⚡ 参考 [性能优化](performance.md) 调优系统
4. 🔧 学习 [故障排除](troubleshooting.md) 解决问题

### 开发者
1. 📋 查看 [项目结构说明](project-structure.md) 了解代码组织
2. 🔌 参考 [API文档](api/README.md) 开发接口
3. 🎨 使用 [前端组件库](frontend/components.md) 构建界面
4. 🗄️ 了解 [数据库设计](database/schema.md) 设计数据模型

## 📊 架构图表

### 系统整体架构
```mermaid
graph TB
    A[用户界面] --> B[API网关]
    B --> C[智能体服务]
    C --> D[数据存储]
    C --> E[AI服务]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#fce4ec
```

### 智能体生态
```mermaid
graph LR
    A[智能客服] --> E[共享服务]
    B[Text2SQL] --> E
    C[知识库问答] --> E
    D[文案创作] --> E
    
    style A fill:#ffebee
    style B fill:#e8f5e8
    style C fill:#e3f2fd
    style D fill:#fff3e0
    style E fill:#f3e5f5
```

## 🔄 文档更新

本文档会随着项目的发展持续更新。如果您发现任何问题或有改进建议，请：

1. 提交 Issue 报告问题
2. 提交 Pull Request 贡献改进
3. 联系开发团队获取支持

## 📞 获取帮助

- **技术支持**: [技术支持邮箱]
- **开发交流**: [开发者群组]
- **问题反馈**: [GitHub Issues]
- **文档贡献**: [贡献指南]

---

> 💡 **提示**: 建议按照文档的顺序阅读，这样可以更好地理解整个系统的设计思路和实现方案。

最后更新时间: 2024年1月1日
