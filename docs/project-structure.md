# YUE智能体综合应用平台 - 项目结构

## 相关文档

- 📋 [系统架构设计](architecture.md) - 详细的技术架构和部署架构
- 🤖 [智能体架构设计](agents-architecture.md) - 四大智能体的详细设计
- 🚀 [快速启动指南](quick-start.md) - 项目安装和启动说明

## 整体架构

```
yue_ai_agent/
├── frontend/                     # 前端项目 (React + TypeScript)
├── backend/                      # 后端项目 (Python + FastAPI)
├── docker/                       # Docker配置文件
├── docs/                         # 项目文档
├── scripts/                      # 部署和工具脚本
├── docker-compose.yml            # Docker Compose配置
└── README.md                     # 项目说明
```

## 前端结构 (frontend/)

```
frontend/
├── public/                       # 静态资源
├── src/
│   ├── components/               # 组件
│   │   ├── common/              # 通用组件
│   │   ├── agents/              # 智能体相关组件
│   │   └── layout/              # 布局组件
│   ├── pages/                   # 页面
│   │   ├── auth/                # 认证页面
│   │   ├── dashboard/           # 仪表板
│   │   └── agents/              # 智能体页面
│   ├── hooks/                   # 自定义Hooks
│   ├── store/                   # 状态管理
│   ├── services/                # API服务
│   ├── types/                   # TypeScript类型定义
│   ├── utils/                   # 工具函数
│   ├── assets/                  # 资源文件
│   └── main.tsx                 # 应用入口
├── package.json                 # 依赖配置
├── vite.config.ts              # Vite配置
├── tailwind.config.js          # Tailwind CSS配置
├── tsconfig.json               # TypeScript配置
└── Dockerfile                  # Docker配置
```

## 后端结构 (backend/)

```
backend/
├── app/
│   ├── api/                     # API路由
│   │   ├── v1/                  # API v1版本
│   │   └── deps/                # 依赖注入
│   ├── core/                    # 核心模块
│   │   ├── config/              # 配置管理
│   │   └── security/            # 安全相关
│   ├── models/                  # 数据模型
│   ├── services/                # 业务服务
│   ├── utils/                   # 工具函数
│   ├── agents/                  # 智能体模块
│   │   ├── customer_service/    # 智能客服
│   │   ├── text2sql/           # Text2SQL分析
│   │   ├── knowledge_base/     # 知识库问答
│   │   └── content_creation/   # 文案创作
│   └── main.py                 # 应用入口
├── tests/                      # 测试文件
├── alembic/                    # 数据库迁移
├── requirements.txt            # Python依赖
├── .env.example               # 环境变量示例
└── Dockerfile                 # Docker配置
```

## 智能体模块详细结构

### 1. 智能客服智能体 (customer_service/)
```
customer_service/
├── __init__.py
├── agent.py                    # 主要智能体逻辑
├── tools.py                    # 工具函数
├── prompts.py                  # 提示词模板
└── models.py                   # 数据模型
```

### 2. Text2SQL数据分析智能体 (text2sql/)
```
text2sql/
├── __init__.py
├── agent.py                    # SQL生成逻辑
├── parser.py                   # SQL解析器
├── executor.py                 # SQL执行器
├── visualizer.py               # 图表生成
└── schemas.py                  # 数据库模式
```

### 3. 企业级知识库问答智能体 (knowledge_base/)
```
knowledge_base/
├── __init__.py
├── agent.py                    # 问答逻辑
├── rag/                        # RAG相关
│   ├── traditional.py          # 传统RAG
│   ├── nano_graph.py          # NanoGraphRAG
│   └── multimodal.py          # 多模态RAG
├── embeddings.py               # 向量化
└── retrieval.py                # 检索逻辑
```

### 4. 企业内部文案创作智能体 (content_creation/)
```
content_creation/
├── __init__.py
├── agent.py                    # 文案生成逻辑
├── templates/                  # 文案模板
├── generators/                 # 生成器
└── exporters.py                # 导出功能
```

## 数据库设计

### MySQL表结构
- users: 用户信息
- sessions: 会话历史
- files: 文件管理
- knowledge_bases: 知识库
- agents: 智能体配置
- conversations: 对话记录

### Milvus集合
- document_embeddings: 文档向量
- image_embeddings: 图像向量
- audio_embeddings: 音频向量

## 技术栈说明

### 前端技术栈
- **React 18**: 现代化的前端框架
- **TypeScript**: 类型安全的JavaScript
- **Vite**: 快速的构建工具
- **Tailwind CSS**: 实用优先的CSS框架
- **Framer Motion**: 动画库
- **Zustand**: 轻量级状态管理
- **React Query**: 数据获取和缓存
- **Socket.io**: 实时通信

### 后端技术栈
- **FastAPI**: 现代化的Python Web框架
- **SQLAlchemy**: ORM框架
- **Alembic**: 数据库迁移工具
- **Celery**: 异步任务队列
- **Redis**: 缓存和消息队列
- **MySQL**: 关系数据库
- **Milvus**: 向量数据库
- **MinIO**: 对象存储

### AI/ML技术栈
- **OpenAI API**: 大语言模型
- **LangChain**: AI应用开发框架
- **Sentence Transformers**: 文本向量化
- **RAG**: 检索增强生成
- **NanoGraphRAG**: 图谱增强检索
