#!/usr/bin/env python3
"""
YUE智能体综合应用平台 - 项目初始化脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """执行命令"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd, 
            capture_output=True, 
            text=True
        )
        if result.returncode != 0:
            print(f"错误: {result.stderr}")
            return False
        print(result.stdout)
        return True
    except Exception as e:
        print(f"执行命令失败: {e}")
        return False

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 11):
        print("❌ Python 版本需要 3.11 或更高")
        return False
    print("✅ Python 版本符合要求")
    
    # 检查 Node.js
    if not run_command("node --version"):
        print("❌ 请安装 Node.js 18 或更高版本")
        return False
    print("✅ Node.js 已安装")
    
    # 检查 Docker
    if not run_command("docker --version"):
        print("⚠️  Docker 未安装，将无法使用容器化部署")
    else:
        print("✅ Docker 已安装")
    
    return True

def setup_backend():
    """设置后端环境"""
    print("\n🐍 设置后端环境...")
    
    backend_dir = Path("backend")
    
    # 创建虚拟环境
    if not Path("venv").exists():
        print("创建虚拟环境...")
        if not run_command("python -m venv venv"):
            return False
    
    # 激活虚拟环境并安装依赖
    if os.name == 'nt':  # Windows
        pip_cmd = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        pip_cmd = "venv/bin/pip"
    
    print("安装后端依赖...")
    if not run_command(f"{pip_cmd} install -r backend/requirements.txt"):
        return False
    
    # 复制环境配置文件
    env_example = backend_dir / ".env.example"
    env_file = backend_dir / ".env"
    
    if env_example.exists() and not env_file.exists():
        shutil.copy(env_example, env_file)
        print("✅ 已创建 .env 配置文件")
    
    print("✅ 后端环境设置完成")
    return True

def setup_frontend():
    """设置前端环境"""
    print("\n⚛️  设置前端环境...")
    
    frontend_dir = Path("frontend")
    
    # 安装前端依赖
    print("安装前端依赖...")
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("✅ 前端环境设置完成")
    return True

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建项目目录...")
    
    directories = [
        "uploads",
        "logs",
        "static",
        "backend/static",
        "backend/uploads",
        "backend/logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")

def main():
    """主函数"""
    print("🚀 YUE智能体综合应用平台 - 项目初始化")
    print("=" * 50)
    
    # 检查系统要求
    if not check_requirements():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 设置后端
    if not setup_backend():
        print("❌ 后端环境设置失败")
        sys.exit(1)
    
    # 设置前端
    if not setup_frontend():
        print("❌ 前端环境设置失败")
        sys.exit(1)
    
    print("\n🎉 项目初始化完成!")
    print("\n📋 下一步:")
    print("1. 配置 backend/.env 文件中的数据库和API密钥")
    print("2. 启动数据库服务 (MySQL, Redis, Milvus)")
    print("3. 运行 'docker-compose up -d' 启动所有服务")
    print("4. 或者分别启动前后端:")
    print("   - 后端: cd backend && uvicorn app.main:socket_app --reload")
    print("   - 前端: cd frontend && npm run dev")

if __name__ == "__main__":
    main()
