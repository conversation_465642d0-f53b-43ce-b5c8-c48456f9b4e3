"""
产品相关API端点
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.core.database import get_db
from app.services.product import ProductService
from app.schemas.customer_service import Product, ProductQuery, Inventory, Category

router = APIRouter()


@router.get("/search", response_model=List[Product])
def search_products(
    name: Optional[str] = Query(None, description="产品名称"),
    model: Optional[str] = Query(None, description="产品型号"),
    sku: Optional[str] = Query(None, description="产品SKU"),
    category_id: Optional[int] = Query(None, description="分类ID"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    min_price: Optional[float] = Query(None, description="最低价格"),
    max_price: Optional[float] = Query(None, description="最高价格"),
    limit: int = Query(20, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    db: Session = Depends(get_db)
):
    """
    场景1: 售前咨询 - 产品信息查询
    支持多种查询条件搜索产品
    """
    query = ProductQuery(
        name=name,
        model=model,
        sku=sku,
        category_id=category_id,
        is_featured=is_featured,
        min_price=min_price,
        max_price=max_price
    )
    
    product_service = ProductService(db)
    products = product_service.search_products(query, limit=limit, offset=offset)
    
    return products


@router.get("/{product_id}", response_model=Product)
def get_product_detail(
    product_id: int,
    db: Session = Depends(get_db)
):
    """
    获取产品详细信息
    包含规格、特性、价格、材质、尺寸指南等
    """
    product_service = ProductService(db)
    product = product_service.get_product_by_id(product_id)
    
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    
    return product


@router.get("/{product_id}/inventory", response_model=Inventory)
def get_product_inventory(
    product_id: int,
    db: Session = Depends(get_db)
):
    """
    查询产品库存状态
    """
    product_service = ProductService(db)
    
    # 先检查产品是否存在
    product = product_service.get_product_by_id(product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    
    inventory = product_service.get_product_inventory(product_id)
    if not inventory:
        raise HTTPException(status_code=404, detail="库存信息不存在")
    
    return inventory


@router.get("/{product_id}/recommendations", response_model=List[Product])
def get_product_recommendations(
    product_id: int,
    limit: int = Query(3, le=10, description="推荐数量"),
    db: Session = Depends(get_db)
):
    """
    获取产品推荐
    基于分类和价格范围推荐相似产品
    """
    product_service = ProductService(db)
    
    # 检查产品是否存在
    product = product_service.get_product_by_id(product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    
    recommendations = product_service.get_product_recommendations(product_id, limit=limit)
    return recommendations


@router.get("/categories/", response_model=List[Category])
def get_categories(
    parent_id: Optional[int] = Query(None, description="父分类ID"),
    db: Session = Depends(get_db)
):
    """
    获取产品分类列表
    """
    product_service = ProductService(db)
    categories = product_service.get_categories(parent_id=parent_id)
    return categories


@router.get("/featured/", response_model=List[Product])
def get_featured_products(
    limit: int = Query(10, le=50, description="返回数量"),
    db: Session = Depends(get_db)
):
    """
    获取推荐产品列表
    """
    product_service = ProductService(db)
    products = product_service.get_featured_products(limit=limit)
    return products


@router.get("/sku/{sku}", response_model=Product)
def get_product_by_sku(
    sku: str,
    db: Session = Depends(get_db)
):
    """
    根据SKU获取产品信息
    """
    product_service = ProductService(db)
    product = product_service.get_product_by_sku(sku)
    
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    
    return product


@router.post("/{product_id}/check-availability")
def check_product_availability(
    product_id: int,
    quantity: int = Query(1, ge=1, description="需要检查的数量"),
    db: Session = Depends(get_db)
):
    """
    检查产品库存是否充足
    """
    product_service = ProductService(db)
    
    # 检查产品是否存在
    product = product_service.get_product_by_id(product_id)
    if not product:
        raise HTTPException(status_code=404, detail="产品不存在")
    
    is_available = product_service.check_product_availability(product_id, quantity)
    inventory = product_service.get_product_inventory(product_id)
    
    return {
        "product_id": product_id,
        "requested_quantity": quantity,
        "available": is_available,
        "available_quantity": inventory.available_quantity if inventory else 0,
        "message": "库存充足" if is_available else "库存不足"
    }
