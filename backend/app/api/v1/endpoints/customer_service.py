"""
客服相关API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_active_user
from app.services.customer_service import CustomerServiceService
from app.schemas.customer_service import (
    RefundRequest, RefundRequestCreate, Complaint, ComplaintCreate,
    CustomerServiceSession, CustomerServiceMessage
)
from app.schemas.user import User
from app.models.customer_service import RefundStatus, ComplaintStatus

router = APIRouter()


# 退换货相关端点
@router.post("/refund-requests", response_model=RefundRequest)
def create_refund_request(
    request_data: RefundRequestCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    场景4: 售后服务 - 退换货申请
    创建退换货申请
    """
    cs_service = CustomerServiceService(db)
    
    # 检查退换货资格
    eligibility = cs_service.check_refund_eligibility(request_data.order_id)
    if not eligibility["eligible"]:
        raise HTTPException(status_code=400, detail=eligibility["reason"])
    
    refund_request = cs_service.create_refund_request(current_user.id, request_data)
    
    return refund_request


@router.get("/refund-requests", response_model=List[RefundRequest])
def get_my_refund_requests(
    limit: int = Query(20, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的退换货申请列表
    """
    cs_service = CustomerServiceService(db)
    requests = cs_service.get_user_refund_requests(current_user.id, limit=limit, offset=offset)
    
    return requests


@router.get("/refund-requests/{request_number}", response_model=RefundRequest)
def get_refund_request_detail(
    request_number: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取退换货申请详情
    """
    cs_service = CustomerServiceService(db)
    refund_request = cs_service.get_refund_request_by_number(request_number)
    
    if not refund_request:
        raise HTTPException(status_code=404, detail="退换货申请不存在")
    
    # 检查权限
    if refund_request.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此申请")
    
    return refund_request


@router.post("/refund-requests/{order_id}/check-eligibility")
def check_refund_eligibility(
    order_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    检查订单是否符合退换货条件
    """
    cs_service = CustomerServiceService(db)
    
    # 检查订单是否属于当前用户
    from app.services.order import OrderService
    order_service = OrderService(db)
    order = order_service.get_order_by_id(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    if order.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此订单")
    
    eligibility = cs_service.check_refund_eligibility(order_id)
    
    return eligibility


# 投诉建议相关端点
@router.post("/complaints", response_model=Complaint)
def create_complaint(
    complaint_data: ComplaintCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    场景5: 投诉与建议
    创建投诉或建议
    """
    cs_service = CustomerServiceService(db)
    complaint = cs_service.create_complaint(current_user.id, complaint_data)
    
    return complaint


@router.get("/complaints", response_model=List[Complaint])
def get_my_complaints(
    limit: int = Query(20, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户的投诉建议列表
    """
    cs_service = CustomerServiceService(db)
    complaints = cs_service.get_user_complaints(current_user.id, limit=limit, offset=offset)
    
    return complaints


@router.get("/complaints/{complaint_number}", response_model=Complaint)
def get_complaint_detail(
    complaint_number: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取投诉建议详情
    """
    cs_service = CustomerServiceService(db)
    complaint = cs_service.get_complaint_by_number(complaint_number)
    
    if not complaint:
        raise HTTPException(status_code=404, detail="投诉建议不存在")
    
    # 检查权限
    if complaint.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此投诉")
    
    return complaint


# 客服会话相关端点
@router.post("/sessions", response_model=CustomerServiceSession)
def create_customer_service_session(
    agent_type: str = Query("ai", description="智能体类型: ai, human"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    创建客服会话
    """
    cs_service = CustomerServiceService(db)
    
    # 检查是否已有活跃会话
    active_session = cs_service.get_user_active_session(current_user.id)
    if active_session:
        return active_session
    
    session = cs_service.create_customer_service_session(current_user.id, agent_type)
    
    return session


@router.get("/sessions/{session_id}", response_model=CustomerServiceSession)
def get_session_detail(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取客服会话详情
    """
    cs_service = CustomerServiceService(db)
    session = cs_service.get_session_by_id(session_id)
    
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    # 检查权限
    if session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    return session


@router.get("/sessions/{session_id}/messages", response_model=List[CustomerServiceMessage])
def get_session_messages(
    session_id: str,
    limit: int = Query(50, le=200, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取会话消息列表
    """
    cs_service = CustomerServiceService(db)
    
    # 检查会话权限
    session = cs_service.get_session_by_id(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    if session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    messages = cs_service.get_session_messages(session_id, limit=limit, offset=offset)
    
    return messages


@router.post("/sessions/{session_id}/messages", response_model=CustomerServiceMessage)
def add_message_to_session(
    session_id: str,
    content: str = Query(..., description="消息内容"),
    message_type: str = Query("text", description="消息类型"),
    metadata: Optional[Dict[str, Any]] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    向会话添加消息
    """
    cs_service = CustomerServiceService(db)
    
    # 检查会话权限
    session = cs_service.get_session_by_id(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    if session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    message = cs_service.add_message_to_session(
        session_id=session_id,
        sender_type="user",
        sender_id=str(current_user.id),
        content=content,
        message_type=message_type,
        metadata=metadata or {}
    )
    
    return message


@router.post("/sessions/{session_id}/transfer-to-human")
def transfer_to_human(
    session_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    场景6: 请求人工服务
    转接到人工客服
    """
    cs_service = CustomerServiceService(db)
    
    # 检查会话权限
    session = cs_service.get_session_by_id(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    if session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    updated_session = cs_service.transfer_to_human(session_id)
    
    if not updated_session:
        raise HTTPException(status_code=400, detail="转接失败")
    
    return {
        "message": "正在为您转接人工客服，请稍候...",
        "session_id": session_id,
        "agent_type": updated_session.agent_type,
        "status": updated_session.status
    }


@router.post("/sessions/{session_id}/close")
def close_session(
    session_id: str,
    satisfaction_rating: Optional[int] = Query(None, ge=1, le=5, description="满意度评分"),
    summary: str = Query("", description="会话总结"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    关闭客服会话
    """
    cs_service = CustomerServiceService(db)
    
    # 检查会话权限
    session = cs_service.get_session_by_id(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    if session.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此会话")
    
    closed_session = cs_service.close_session(session_id, satisfaction_rating, summary)
    
    return {
        "message": "会话已关闭",
        "session_id": session_id,
        "satisfaction_rating": satisfaction_rating,
        "end_time": closed_session.end_time
    }


# 管理员端点
@router.put("/refund-requests/{request_id}/status")
def update_refund_request_status(
    request_id: int,
    status: RefundStatus,
    admin_notes: str = Query("", description="管理员备注"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新退换货申请状态（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    cs_service = CustomerServiceService(db)
    refund_request = cs_service.update_refund_request_status(request_id, status, admin_notes)
    
    if not refund_request:
        raise HTTPException(status_code=404, detail="退换货申请不存在")
    
    return {
        "message": "退换货申请状态更新成功",
        "request_number": refund_request.request_number,
        "status": refund_request.status.value
    }


@router.put("/complaints/{complaint_id}/status")
def update_complaint_status(
    complaint_id: int,
    status: ComplaintStatus,
    resolution: str = Query("", description="处理结果"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新投诉状态（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    cs_service = CustomerServiceService(db)
    complaint = cs_service.update_complaint_status(complaint_id, status, resolution)
    
    if not complaint:
        raise HTTPException(status_code=404, detail="投诉建议不存在")
    
    return {
        "message": "投诉状态更新成功",
        "complaint_number": complaint.complaint_number,
        "status": complaint.status.value
    }


@router.get("/statistics")
def get_customer_service_statistics(
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取客服统计信息（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    cs_service = CustomerServiceService(db)
    stats = cs_service.get_customer_service_statistics(start_date, end_date)
    
    return stats
