"""
订单相关API端点
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from datetime import datetime

from app.core.database import get_db
from app.api.deps import get_current_active_user
from app.services.order import OrderService
from app.schemas.customer_service import Order, OrderCreate, OrderUpdate, OrderQuery
from app.schemas.user import User
from app.models.customer_service import OrderStatus

router = APIRouter()


@router.get("/search", response_model=List[Order])
def search_orders(
    order_number: Optional[str] = Query(None, description="订单号"),
    status: Optional[OrderStatus] = Query(None, description="订单状态"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    limit: int = Query(20, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    搜索订单
    普通用户只能查看自己的订单
    """
    query = OrderQuery(
        order_number=order_number,
        user_id=current_user.id,  # 限制只能查看自己的订单
        status=status,
        start_date=start_date,
        end_date=end_date
    )
    
    order_service = OrderService(db)
    orders = order_service.search_orders(query, limit=limit, offset=offset)
    
    return orders


@router.get("/my-orders", response_model=List[Order])
def get_my_orders(
    limit: int = Query(20, le=100, description="返回数量限制"),
    offset: int = Query(0, ge=0, description="偏移量"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取当前用户的订单列表
    """
    order_service = OrderService(db)
    orders = order_service.get_user_orders(current_user.id, limit=limit, offset=offset)
    
    return orders


@router.get("/{order_number}/tracking")
def get_order_tracking(
    order_number: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    场景3: 订单追踪
    查询订单状态和物流信息
    """
    order_service = OrderService(db)
    
    # 获取订单
    order = order_service.get_order_by_number(order_number)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 检查权限（用户只能查看自己的订单）
    if order.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此订单")
    
    # 获取跟踪信息
    tracking_info = order_service.get_order_tracking_info(order_number)
    
    return tracking_info


@router.get("/{order_id}", response_model=Order)
def get_order_detail(
    order_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取订单详细信息
    """
    order_service = OrderService(db)
    order = order_service.get_order_by_id(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 检查权限
    if order.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此订单")
    
    return order


@router.post("/", response_model=Order)
def create_order(
    order_data: OrderCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    创建订单
    """
    # 设置用户ID
    order_data.user_id = current_user.id
    
    order_service = OrderService(db)
    order = order_service.create_order(order_data)
    
    return order


@router.put("/{order_id}/cancel")
def cancel_order(
    order_id: int,
    reason: str = Query("", description="取消原因"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    取消订单
    """
    order_service = OrderService(db)
    order = order_service.get_order_by_id(order_id)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    # 检查权限
    if order.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权操作此订单")
    
    cancelled_order = order_service.cancel_order(order_id, reason)
    
    if not cancelled_order:
        raise HTTPException(status_code=400, detail="订单状态不允许取消")
    
    return {
        "message": "订单已取消",
        "order_number": cancelled_order.order_number,
        "status": cancelled_order.status.value
    }


@router.get("/statistics/summary")
def get_order_statistics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    获取用户订单统计信息
    """
    order_service = OrderService(db)
    stats = order_service.get_order_statistics(user_id=current_user.id)
    
    return stats


@router.post("/{order_id}/logistics")
def create_logistics_tracking(
    order_id: int,
    tracking_data: Dict[str, Any],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    创建物流跟踪记录（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    order_service = OrderService(db)
    
    # 检查订单是否存在
    order = order_service.get_order_by_id(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    logistics = order_service.create_logistics_tracking(order_id, tracking_data)
    
    return {
        "message": "物流跟踪记录创建成功",
        "tracking_number": logistics.tracking_number,
        "logistics_company": logistics.logistics_company
    }


@router.put("/{order_id}/logistics")
def update_logistics_tracking(
    order_id: int,
    tracking_info: List[Dict[str, Any]],
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新物流跟踪信息（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    order_service = OrderService(db)
    
    # 检查订单是否存在
    order = order_service.get_order_by_id(order_id)
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    logistics = order_service.update_logistics_tracking(order_id, tracking_info)
    
    if not logistics:
        raise HTTPException(status_code=404, detail="物流跟踪记录不存在")
    
    return {
        "message": "物流跟踪信息更新成功",
        "current_status": logistics.current_status,
        "last_updated": logistics.last_updated
    }


@router.put("/{order_id}/status")
def update_order_status(
    order_id: int,
    status: OrderStatus,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    更新订单状态（管理员功能）
    """
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    order_service = OrderService(db)
    order = order_service.update_order_status(order_id, status)
    
    if not order:
        raise HTTPException(status_code=404, detail="订单不存在")
    
    return {
        "message": "订单状态更新成功",
        "order_number": order.order_number,
        "status": order.status.value
    }
