"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import products, orders, promotions, customer_service

api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(products.router, prefix="/products", tags=["products"])
api_router.include_router(orders.router, prefix="/orders", tags=["orders"])
api_router.include_router(promotions.router, prefix="/promotions", tags=["promotions"])
api_router.include_router(customer_service.router, prefix="/customer-service", tags=["customer-service"])
