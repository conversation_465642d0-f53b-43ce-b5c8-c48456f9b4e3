"""
客服相关业务服务
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime
import uuid

from app.models.customer_service import (
    RefundRequest, Complaint, CustomerServiceSession, CustomerServiceMessage,
    RefundStatus, ComplaintStatus
)
from app.schemas.customer_service import RefundRequestCreate, ComplaintCreate


class CustomerServiceService:
    """客服服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def generate_request_number(self, prefix: str = "REQ") -> str:
        """生成申请单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4().hex)[:6].upper()
        return f"{prefix}{timestamp}{random_suffix}"
    
    # 退换货相关方法
    def create_refund_request(self, user_id: int, request_data: RefundRequestCreate) -> RefundRequest:
        """创建退换货申请"""
        request_number = self.generate_request_number("REF")
        
        refund_request = RefundRequest(
            request_number=request_number,
            user_id=user_id,
            **request_data.dict()
        )
        
        self.db.add(refund_request)
        self.db.commit()
        self.db.refresh(refund_request)
        return refund_request
    
    def get_refund_request_by_number(self, request_number: str) -> Optional[RefundRequest]:
        """根据申请单号获取退换货申请"""
        return self.db.query(RefundRequest).filter(
            RefundRequest.request_number == request_number
        ).first()
    
    def get_user_refund_requests(self, user_id: int, limit: int = 20, offset: int = 0) -> List[RefundRequest]:
        """获取用户的退换货申请列表"""
        return self.db.query(RefundRequest).filter(
            RefundRequest.user_id == user_id
        ).order_by(desc(RefundRequest.created_at)).offset(offset).limit(limit).all()
    
    def update_refund_request_status(self, request_id: int, status: RefundStatus, admin_notes: str = "") -> Optional[RefundRequest]:
        """更新退换货申请状态"""
        refund_request = self.db.query(RefundRequest).filter(RefundRequest.id == request_id).first()
        if not refund_request:
            return None
        
        refund_request.status = status
        if admin_notes:
            refund_request.admin_notes = admin_notes
        
        if status in [RefundStatus.APPROVED, RefundStatus.REJECTED, RefundStatus.COMPLETED]:
            refund_request.processed_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(refund_request)
        return refund_request
    
    def check_refund_eligibility(self, order_id: int) -> Dict[str, Any]:
        """检查订单是否符合退换货条件"""
        from app.services.order import OrderService
        
        order_service = OrderService(self.db)
        order = order_service.get_order_by_id(order_id)
        
        if not order:
            return {"eligible": False, "reason": "订单不存在"}
        
        # 检查订单状态
        from app.models.customer_service import OrderStatus
        if order.status not in [OrderStatus.DELIVERED, OrderStatus.SHIPPED]:
            return {"eligible": False, "reason": "订单状态不符合退换货条件"}
        
        # 检查时间限制（假设7天内可退换货）
        days_since_delivery = (datetime.now() - order.created_at).days
        if days_since_delivery > 7:
            return {"eligible": False, "reason": "已超过退换货期限（7天）"}
        
        # 检查是否已有退换货申请
        existing_request = self.db.query(RefundRequest).filter(
            and_(
                RefundRequest.order_id == order_id,
                RefundRequest.status.in_([RefundStatus.PENDING, RefundStatus.APPROVED, RefundStatus.PROCESSING])
            )
        ).first()
        
        if existing_request:
            return {"eligible": False, "reason": "该订单已有退换货申请正在处理中"}
        
        return {"eligible": True, "reason": "符合退换货条件"}
    
    # 投诉建议相关方法
    def create_complaint(self, user_id: int, complaint_data: ComplaintCreate) -> Complaint:
        """创建投诉建议"""
        complaint_number = self.generate_request_number("CMP")
        
        complaint = Complaint(
            complaint_number=complaint_number,
            user_id=user_id,
            **complaint_data.dict()
        )
        
        self.db.add(complaint)
        self.db.commit()
        self.db.refresh(complaint)
        return complaint
    
    def get_complaint_by_number(self, complaint_number: str) -> Optional[Complaint]:
        """根据投诉单号获取投诉"""
        return self.db.query(Complaint).filter(
            Complaint.complaint_number == complaint_number
        ).first()
    
    def get_user_complaints(self, user_id: int, limit: int = 20, offset: int = 0) -> List[Complaint]:
        """获取用户的投诉建议列表"""
        return self.db.query(Complaint).filter(
            Complaint.user_id == user_id
        ).order_by(desc(Complaint.created_at)).offset(offset).limit(limit).all()
    
    def update_complaint_status(self, complaint_id: int, status: ComplaintStatus, resolution: str = "") -> Optional[Complaint]:
        """更新投诉状态"""
        complaint = self.db.query(Complaint).filter(Complaint.id == complaint_id).first()
        if not complaint:
            return None
        
        complaint.status = status
        if resolution:
            complaint.resolution = resolution
        
        self.db.commit()
        self.db.refresh(complaint)
        return complaint
    
    # 客服会话相关方法
    def create_customer_service_session(self, user_id: int, agent_type: str = "ai") -> CustomerServiceSession:
        """创建客服会话"""
        session_id = str(uuid.uuid4())
        
        session = CustomerServiceSession(
            session_id=session_id,
            user_id=user_id,
            agent_type=agent_type,
            agent_id=f"{agent_type}_agent_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        )
        
        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        return session
    
    def get_session_by_id(self, session_id: str) -> Optional[CustomerServiceSession]:
        """根据会话ID获取会话"""
        return self.db.query(CustomerServiceSession).filter(
            CustomerServiceSession.session_id == session_id
        ).first()
    
    def get_user_active_session(self, user_id: int) -> Optional[CustomerServiceSession]:
        """获取用户当前活跃的会话"""
        return self.db.query(CustomerServiceSession).filter(
            and_(
                CustomerServiceSession.user_id == user_id,
                CustomerServiceSession.status == "active"
            )
        ).first()
    
    def add_message_to_session(self, session_id: str, sender_type: str, sender_id: str, 
                              content: str, message_type: str = "text", metadata: Dict[str, Any] = None) -> CustomerServiceMessage:
        """向会话添加消息"""
        message = CustomerServiceMessage(
            session_id=session_id,
            sender_type=sender_type,
            sender_id=sender_id,
            message_type=message_type,
            content=content,
            metadata=metadata or {}
        )
        
        self.db.add(message)
        self.db.commit()
        self.db.refresh(message)
        return message
    
    def get_session_messages(self, session_id: str, limit: int = 50, offset: int = 0) -> List[CustomerServiceMessage]:
        """获取会话消息"""
        return self.db.query(CustomerServiceMessage).filter(
            CustomerServiceMessage.session_id == session_id
        ).order_by(CustomerServiceMessage.created_at).offset(offset).limit(limit).all()
    
    def close_session(self, session_id: str, satisfaction_rating: Optional[int] = None, summary: str = "") -> Optional[CustomerServiceSession]:
        """关闭客服会话"""
        session = self.get_session_by_id(session_id)
        if not session:
            return None
        
        session.status = "closed"
        session.end_time = datetime.now()
        if satisfaction_rating:
            session.satisfaction_rating = satisfaction_rating
        if summary:
            session.summary = summary
        
        self.db.commit()
        self.db.refresh(session)
        return session
    
    def transfer_to_human(self, session_id: str) -> Optional[CustomerServiceSession]:
        """转接到人工客服"""
        session = self.get_session_by_id(session_id)
        if not session:
            return None
        
        session.agent_type = "human"
        session.status = "transferred"
        
        # 添加系统消息
        self.add_message_to_session(
            session_id=session_id,
            sender_type="system",
            sender_id="system",
            content="正在为您转接人工客服，请稍候...",
            message_type="system"
        )
        
        self.db.commit()
        self.db.refresh(session)
        return session
    
    def get_customer_service_statistics(self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """获取客服统计信息"""
        query = self.db.query(CustomerServiceSession)
        
        if start_date:
            query = query.filter(CustomerServiceSession.start_time >= start_date)
        if end_date:
            query = query.filter(CustomerServiceSession.start_time <= end_date)
        
        total_sessions = query.count()
        ai_sessions = query.filter(CustomerServiceSession.agent_type == "ai").count()
        human_sessions = query.filter(CustomerServiceSession.agent_type == "human").count()
        
        # 满意度统计
        satisfaction_ratings = query.filter(
            CustomerServiceSession.satisfaction_rating.isnot(None)
        ).with_entities(CustomerServiceSession.satisfaction_rating).all()
        
        avg_satisfaction = 0
        if satisfaction_ratings:
            avg_satisfaction = sum(rating[0] for rating in satisfaction_ratings) / len(satisfaction_ratings)
        
        return {
            "total_sessions": total_sessions,
            "ai_sessions": ai_sessions,
            "human_sessions": human_sessions,
            "average_satisfaction": round(avg_satisfaction, 2),
            "satisfaction_count": len(satisfaction_ratings)
        }
