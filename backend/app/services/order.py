"""
订单相关业务服务
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime
import uuid

from app.models.customer_service import Order, OrderItem, LogisticsTracking, OrderStatus
from app.schemas.customer_service import OrderCreate, OrderUpdate, OrderQuery


class OrderService:
    """订单服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def generate_order_number(self) -> str:
        """生成订单号"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = str(uuid.uuid4().hex)[:6].upper()
        return f"ORD{timestamp}{random_suffix}"
    
    def get_order_by_id(self, order_id: int) -> Optional[Order]:
        """根据ID获取订单"""
        return self.db.query(Order).filter(Order.id == order_id).first()
    
    def get_order_by_number(self, order_number: str) -> Optional[Order]:
        """根据订单号获取订单"""
        return self.db.query(Order).filter(Order.order_number == order_number).first()
    
    def get_user_orders(self, user_id: int, limit: int = 20, offset: int = 0) -> List[Order]:
        """获取用户订单列表"""
        return self.db.query(Order).filter(
            Order.user_id == user_id
        ).order_by(desc(Order.created_at)).offset(offset).limit(limit).all()
    
    def search_orders(self, query: OrderQuery, limit: int = 20, offset: int = 0) -> List[Order]:
        """搜索订单"""
        db_query = self.db.query(Order)
        
        # 构建查询条件
        conditions = []
        
        if query.order_number:
            conditions.append(Order.order_number == query.order_number)
        
        if query.user_id:
            conditions.append(Order.user_id == query.user_id)
        
        if query.status:
            conditions.append(Order.status == query.status)
        
        if query.start_date:
            conditions.append(Order.created_at >= query.start_date)
        
        if query.end_date:
            conditions.append(Order.created_at <= query.end_date)
        
        if conditions:
            db_query = db_query.filter(and_(*conditions))
        
        return db_query.order_by(desc(Order.created_at)).offset(offset).limit(limit).all()
    
    def create_order(self, order_data: OrderCreate) -> Order:
        """创建订单"""
        # 生成订单号
        order_number = self.generate_order_number()
        
        # 创建订单
        order_dict = order_data.dict(exclude={'order_items'})
        order_dict['order_number'] = order_number
        order_dict['status'] = OrderStatus.PENDING_PAYMENT
        
        db_order = Order(**order_dict)
        self.db.add(db_order)
        self.db.flush()  # 获取订单ID
        
        # 创建订单商品
        for item_data in order_data.order_items:
            order_item = OrderItem(
                order_id=db_order.id,
                **item_data.dict()
            )
            self.db.add(order_item)
        
        self.db.commit()
        self.db.refresh(db_order)
        return db_order
    
    def update_order_status(self, order_id: int, status: OrderStatus) -> Optional[Order]:
        """更新订单状态"""
        db_order = self.get_order_by_id(order_id)
        if not db_order:
            return None
        
        db_order.status = status
        self.db.commit()
        self.db.refresh(db_order)
        return db_order
    
    def get_order_logistics(self, order_id: int) -> Optional[LogisticsTracking]:
        """获取订单物流信息"""
        return self.db.query(LogisticsTracking).filter(
            LogisticsTracking.order_id == order_id
        ).first()
    
    def create_logistics_tracking(self, order_id: int, tracking_data: Dict[str, Any]) -> LogisticsTracking:
        """创建物流跟踪记录"""
        logistics = LogisticsTracking(
            order_id=order_id,
            **tracking_data
        )
        self.db.add(logistics)
        self.db.commit()
        self.db.refresh(logistics)
        return logistics
    
    def update_logistics_tracking(self, order_id: int, tracking_info: List[Dict[str, Any]]) -> Optional[LogisticsTracking]:
        """更新物流跟踪信息"""
        logistics = self.get_order_logistics(order_id)
        if not logistics:
            return None
        
        logistics.tracking_info = tracking_info
        logistics.last_updated = datetime.now()
        
        # 更新当前状态（取最新的跟踪信息）
        if tracking_info:
            latest_info = tracking_info[-1]
            logistics.current_status = latest_info.get('status', '')
        
        self.db.commit()
        self.db.refresh(logistics)
        return logistics
    
    def get_order_tracking_info(self, order_number: str) -> Optional[Dict[str, Any]]:
        """获取订单跟踪信息"""
        order = self.get_order_by_number(order_number)
        if not order:
            return None
        
        logistics = self.get_order_logistics(order.id)
        
        result = {
            "order": {
                "order_number": order.order_number,
                "status": order.status.value,
                "created_at": order.created_at,
                "total_amount": float(order.total_amount)
            }
        }
        
        if logistics:
            result["logistics"] = {
                "tracking_number": logistics.tracking_number,
                "logistics_company": logistics.logistics_company,
                "current_status": logistics.current_status,
                "estimated_delivery": logistics.estimated_delivery,
                "actual_delivery": logistics.actual_delivery,
                "tracking_info": logistics.tracking_info or [],
                "last_updated": logistics.last_updated
            }
        
        return result
    
    def cancel_order(self, order_id: int, reason: str = "") -> Optional[Order]:
        """取消订单"""
        db_order = self.get_order_by_id(order_id)
        if not db_order:
            return None
        
        # 只有待付款和已付款待发货的订单可以取消
        if db_order.status not in [OrderStatus.PENDING_PAYMENT, OrderStatus.PAID_PENDING_SHIP]:
            return None
        
        db_order.status = OrderStatus.CANCELLED
        if reason:
            db_order.notes = f"{db_order.notes or ''}\n取消原因: {reason}".strip()
        
        self.db.commit()
        self.db.refresh(db_order)
        return db_order
    
    def get_order_statistics(self, user_id: Optional[int] = None) -> Dict[str, Any]:
        """获取订单统计信息"""
        query = self.db.query(Order)
        if user_id:
            query = query.filter(Order.user_id == user_id)
        
        total_orders = query.count()
        
        # 按状态统计
        status_stats = {}
        for status in OrderStatus:
            count = query.filter(Order.status == status).count()
            status_stats[status.value] = count
        
        # 总金额统计
        total_amount = query.filter(
            Order.status.in_([OrderStatus.DELIVERED, OrderStatus.SHIPPED])
        ).with_entities(Order.total_amount).all()
        
        total_revenue = sum(float(amount[0]) for amount in total_amount if amount[0])
        
        return {
            "total_orders": total_orders,
            "status_statistics": status_stats,
            "total_revenue": total_revenue
        }
