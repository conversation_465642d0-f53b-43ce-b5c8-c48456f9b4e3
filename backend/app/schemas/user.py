"""
用户相关Pydantic模型
"""

from typing import Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime


class UserBase(BaseModel):
    """用户基础模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    full_name: Optional[str] = None
    is_active: bool = True
    is_vip: bool = False
    vip_level: int = 0
    avatar_url: Optional[str] = None
    address: Optional[str] = None


class UserCreate(UserBase):
    """创建用户模型"""
    username: str
    email: EmailStr
    password: str


class UserUpdate(UserBase):
    """更新用户模型"""
    password: Optional[str] = None


class UserInDB(UserBase):
    """数据库中的用户模型"""
    id: int
    hashed_password: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class User(UserBase):
    """用户响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
