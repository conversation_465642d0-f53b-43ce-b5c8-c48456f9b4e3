"""
客服相关Pydantic模型
"""

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from decimal import Decimal
from enum import Enum

from app.models.customer_service import OrderStatus, RefundStatus, ComplaintStatus, PromotionType


# 产品相关模型
class CategoryBase(BaseModel):
    """分类基础模型"""
    name: str
    parent_id: Optional[int] = None
    description: Optional[str] = None
    is_active: bool = True


class CategoryCreate(CategoryBase):
    """创建分类模型"""
    pass


class Category(CategoryBase):
    """分类响应模型"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class ProductBase(BaseModel):
    """产品基础模型"""
    name: str
    model: Optional[str] = None
    sku: str
    description: Optional[str] = None
    specifications: Optional[Dict[str, Any]] = None
    price: Decimal
    original_price: Optional[Decimal] = None
    material: Optional[str] = None
    size_guide: Optional[str] = None
    images: Optional[List[str]] = None
    category_id: Optional[int] = None
    is_active: bool = True
    is_featured: bool = False


class ProductCreate(ProductBase):
    """创建产品模型"""
    pass


class ProductUpdate(BaseModel):
    """更新产品模型"""
    name: Optional[str] = None
    model: Optional[str] = None
    description: Optional[str] = None
    specifications: Optional[Dict[str, Any]] = None
    price: Optional[Decimal] = None
    original_price: Optional[Decimal] = None
    material: Optional[str] = None
    size_guide: Optional[str] = None
    images: Optional[List[str]] = None
    category_id: Optional[int] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None


class Product(ProductBase):
    """产品响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    category: Optional[Category] = None

    class Config:
        from_attributes = True


class InventoryBase(BaseModel):
    """库存基础模型"""
    stock_quantity: int = 0
    reserved_quantity: int = 0
    available_quantity: int = 0
    low_stock_threshold: int = 10


class Inventory(InventoryBase):
    """库存响应模型"""
    id: int
    product_id: int
    updated_at: datetime

    class Config:
        from_attributes = True


# 订单相关模型
class OrderItemBase(BaseModel):
    """订单商品基础模型"""
    product_id: int
    quantity: int
    unit_price: Decimal
    total_price: Decimal
    specifications: Optional[Dict[str, Any]] = None


class OrderItem(OrderItemBase):
    """订单商品响应模型"""
    id: int
    order_id: int
    product: Optional[Product] = None

    class Config:
        from_attributes = True


class OrderBase(BaseModel):
    """订单基础模型"""
    user_id: int
    total_amount: Decimal
    discount_amount: Decimal = 0
    shipping_fee: Decimal = 0
    final_amount: Decimal
    shipping_address: Dict[str, Any]
    shipping_method: Optional[str] = None
    payment_method: Optional[str] = None
    notes: Optional[str] = None


class OrderCreate(OrderBase):
    """创建订单模型"""
    order_items: List[OrderItemBase]


class OrderUpdate(BaseModel):
    """更新订单模型"""
    status: Optional[OrderStatus] = None
    shipping_method: Optional[str] = None
    notes: Optional[str] = None


class Order(OrderBase):
    """订单响应模型"""
    id: int
    order_number: str
    status: OrderStatus
    created_at: datetime
    updated_at: datetime
    order_items: List[OrderItem] = []

    class Config:
        from_attributes = True


class LogisticsTrackingBase(BaseModel):
    """物流跟踪基础模型"""
    tracking_number: str
    logistics_company: str
    logistics_company_code: Optional[str] = None
    current_status: Optional[str] = None
    estimated_delivery: Optional[datetime] = None
    tracking_info: Optional[List[Dict[str, Any]]] = None


class LogisticsTracking(LogisticsTrackingBase):
    """物流跟踪响应模型"""
    id: int
    order_id: int
    actual_delivery: Optional[datetime] = None
    last_updated: datetime

    class Config:
        from_attributes = True


# 促销相关模型
class PromotionBase(BaseModel):
    """促销活动基础模型"""
    name: str
    description: Optional[str] = None
    type: PromotionType
    rules: Dict[str, Any]
    start_time: datetime
    end_time: datetime
    is_active: bool = True
    priority: int = 0


class Promotion(PromotionBase):
    """促销活动响应模型"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class CouponBase(BaseModel):
    """优惠券基础模型"""
    code: str
    name: str
    promotion_id: Optional[int] = None
    discount_type: str  # percentage, fixed_amount
    discount_value: Decimal
    min_order_amount: Optional[Decimal] = None
    max_discount_amount: Optional[Decimal] = None
    usage_limit: Optional[int] = None
    start_time: datetime
    end_time: datetime
    is_active: bool = True


class Coupon(CouponBase):
    """优惠券响应模型"""
    id: int
    used_count: int = 0
    created_at: datetime

    class Config:
        from_attributes = True


# 售后相关模型
class RefundRequestBase(BaseModel):
    """退换货申请基础模型"""
    order_id: int
    type: str  # refund, exchange
    reason: str
    description: Optional[str] = None
    refund_amount: Optional[Decimal] = None
    evidence_images: Optional[List[str]] = None


class RefundRequestCreate(RefundRequestBase):
    """创建退换货申请模型"""
    pass


class RefundRequest(RefundRequestBase):
    """退换货申请响应模型"""
    id: int
    request_number: str
    user_id: int
    status: RefundStatus
    admin_notes: Optional[str] = None
    processed_by: Optional[int] = None
    processed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class ComplaintBase(BaseModel):
    """投诉建议基础模型"""
    type: str  # complaint, suggestion
    category: str
    title: str
    content: str
    priority: str = "normal"


class ComplaintCreate(ComplaintBase):
    """创建投诉建议模型"""
    pass


class Complaint(ComplaintBase):
    """投诉建议响应模型"""
    id: int
    complaint_number: str
    user_id: int
    status: ComplaintStatus
    assigned_to: Optional[int] = None
    resolution: Optional[str] = None
    satisfaction_rating: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 客服相关模型
class CustomerServiceMessageBase(BaseModel):
    """客服消息基础模型"""
    sender_type: str  # user, agent
    sender_id: str
    message_type: str = "text"  # text, image, file, system
    content: str
    metadata: Optional[Dict[str, Any]] = None


class CustomerServiceMessage(CustomerServiceMessageBase):
    """客服消息响应模型"""
    id: int
    session_id: str
    is_read: bool = False
    created_at: datetime

    class Config:
        from_attributes = True


class CustomerServiceSessionBase(BaseModel):
    """客服会话基础模型"""
    user_id: int
    agent_type: str = "ai"  # ai, human
    agent_id: str
    status: str = "active"  # active, closed, transferred
    tags: Optional[List[str]] = None


class CustomerServiceSession(CustomerServiceSessionBase):
    """客服会话响应模型"""
    id: int
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    satisfaction_rating: Optional[int] = None
    summary: Optional[str] = None
    messages: List[CustomerServiceMessage] = []

    class Config:
        from_attributes = True


# 查询参数模型
class ProductQuery(BaseModel):
    """产品查询参数"""
    name: Optional[str] = None
    model: Optional[str] = None
    sku: Optional[str] = None
    category_id: Optional[int] = None
    is_active: Optional[bool] = None
    is_featured: Optional[bool] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None


class OrderQuery(BaseModel):
    """订单查询参数"""
    order_number: Optional[str] = None
    user_id: Optional[int] = None
    status: Optional[OrderStatus] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
