"""
YUE智能体综合应用平台 - 主应用入口
"""

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
import socketio
import uvicorn
from loguru import logger

from app.core.config import settings
from app.core.exceptions import CustomException
from app.api.v1.api import api_router
from app.core.middleware import LoggingMiddleware, TimingMiddleware

# 创建 Socket.IO 服务器
sio = socketio.AsyncServer(
    async_mode='asgi',
    cors_allowed_origins=settings.CORS_ORIGINS,
    logger=True,
    engineio_logger=True
)

# 创建 FastAPI 应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    description="企业级AI智能体平台，提供智能客服、数据分析、知识库问答、文案创作等服务",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json" if settings.DEBUG else None,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

app.add_middleware(TimingMiddleware)
app.add_middleware(LoggingMiddleware)

# 异常处理器
@app.exception_handler(CustomException)
async def custom_exception_handler(request: Request, exc: CustomException):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.error_code,
            "message": exc.message,
            "detail": exc.detail
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "Internal server error",
            "detail": str(exc) if settings.DEBUG else None
        }
    )

# 路由
app.include_router(api_router, prefix=settings.API_V1_STR)

# 静态文件服务
if settings.SERVE_STATIC:
    app.mount("/static", StaticFiles(directory="static"), name="static")

# Socket.IO 事件处理
@sio.event
async def connect(sid, environ):
    logger.info(f"Client {sid} connected")

@sio.event
async def disconnect(sid):
    logger.info(f"Client {sid} disconnected")

# 将 Socket.IO 集成到 FastAPI
socket_app = socketio.ASGIApp(sio, app)

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.VERSION}

if __name__ == "__main__":
    uvicorn.run(
        "main:socket_app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
