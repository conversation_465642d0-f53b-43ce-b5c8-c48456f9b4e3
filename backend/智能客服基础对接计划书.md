# 智能客服智能体与电商系统基础对接计划书

## 项目概述

### 项目目标
完成智能客服智能体与现有电商系统的基础对接，实现核心客服功能的稳定运行，为用户提供智能化的客服体验。

### 项目范围
- **包含**：用户身份认证集成、核心工具函数完善、API接口适配、基础客服场景实现
- **不包含**：高级推荐算法、人工转接、多模态支持等增强功能

### 项目周期
**预计工期：** 2周（10个工作日）
**开始时间：** 待定
**结束时间：** 待定

## 当前状态分析

### 已完成组件
✅ **智能体核心框架**
- ChatService - 聊天服务主控制器
- MemoryServiceFactory - 记忆服务工厂
- 三层记忆架构（聊天历史/公共知识/私有记忆）
- AutoGen Agent集成

✅ **基础工具函数**
- get_product_details() - 产品详情查询
- search_products() - 产品搜索
- get_order_status() - 订单状态查询
- get_active_promotions() - 活跃促销查询
- get_policy() - 政策查询
- check_return_eligibility() - 退换货资格检查
- submit_return_request() - 提交退换货申请
- cancel_order() - 订单取消

✅ **电商API接口**
- 产品模块：搜索、详情、库存查询
- 订单模块：查询、状态跟踪、取消
- 促销模块：活跃促销、优惠券管理
- 客服模块：退换货、投诉处理

### 待解决问题
❌ **用户身份认证缺失**
- 工具函数无法获取当前用户身份
- 无法访问用户特定数据（订单、优惠券等）
- 缺少API调用的认证机制

❌ **工具函数不完整**
- 缺少用户优惠券查询
- 缺少物流跟踪功能
- 缺少投诉提交功能
- 缺少退换货状态查询

❌ **API接口适配不完善**
- 错误处理机制不统一
- 响应格式需要优化
- 缺少重试机制

## 详细实施计划

### 第一阶段：用户身份认证集成（3天）

#### 任务1.1：ChatService用户认证改造（1天）
**文件：** `app/agents/customer_service/chat_service.py`

**具体任务：**
1. 修改 `chat_stream()` 方法签名，添加 `user_token` 参数
2. 在创建AssistantAgent时传递用户上下文
3. 实现用户Token验证逻辑
4. 添加用户会话权限检查

**预期输出：**
```python
async def chat_stream(self, messages: List[ChatMessage],
                     system_prompt: Optional[str] = None,
                     user_id: str = "default",
                     user_token: Optional[str] = None,
                     session_id: Optional[str] = None) -> AsyncGenerator[str, None]:
```

#### 任务1.2：工具函数认证机制（1天）
**文件：** `app/services/agent_tools.py`

**具体任务：**
1. 修改 `_call_api()` 函数，支持Authorization头
2. 添加用户上下文类 `UserContext`
3. 实现Token传递机制
4. 添加认证失败处理逻辑

**预期输出：**
```python
class UserContext:
    def __init__(self, user_id: str, token: str):
        self.user_id = user_id
        self.token = token

def _call_api(method: str, endpoint: str, user_context: Optional[UserContext] = None, 
              params: Optional[Dict] = None, json_data: Optional[Dict] = None) -> Union[Dict, str]:
```

#### 任务1.3：用户认证测试（1天）
**文件：** `tests/test_auth_integration.py`

**具体任务：**
1. 编写用户认证单元测试
2. 测试Token传递机制
3. 测试认证失败场景
4. 验证用户权限控制

### 第二阶段：工具函数补全（4天）

#### 任务2.1：用户相关工具函数（1天）
**文件：** `app/services/agent_tools.py`

**新增函数：**
1. `get_user_coupons(user_context)` - 获取用户优惠券列表
2. `get_user_profile(user_context)` - 获取用户基本信息
3. `get_user_orders(user_context, limit=10)` - 获取用户订单列表

**API对接：**
- `GET /api/v1/promotions/coupons/my` - 用户优惠券
- `GET /api/v1/users/profile` - 用户信息
- `GET /api/v1/orders/my-orders` - 用户订单

#### 任务2.2：物流跟踪工具函数（1天）
**文件：** `app/services/agent_tools.py`

**新增函数：**
1. `get_logistics_tracking(order_number, user_context)` - 物流跟踪查询
2. `get_delivery_estimate(order_id, user_context)` - 配送时间预估

**API对接：**
- `GET /api/v1/orders/{order_id}/logistics` - 物流信息
- 需要新增物流跟踪API接口

#### 任务2.3：售后服务工具函数（1天）
**文件：** `app/services/agent_tools.py`

**新增函数：**
1. `get_return_status(request_id, user_context)` - 退换货状态查询
2. `submit_complaint(complaint_data, user_context)` - 投诉建议提交
3. `get_complaint_status(complaint_id, user_context)` - 投诉状态查询

**API对接：**
- `GET /api/v1/customer-service/refund-requests/{id}` - 退换货状态
- `POST /api/v1/customer-service/complaints` - 投诉提交
- `GET /api/v1/customer-service/complaints/{id}` - 投诉状态

#### 任务2.4：促销相关工具函数（1天）
**文件：** `app/services/agent_tools.py`

**新增函数：**
1. `get_applicable_promotions(order_amount, product_ids, user_context)` - 适用促销查询
2. `validate_coupon(coupon_code, order_amount, user_context)` - 优惠券验证
3. `get_coupon_details(coupon_code)` - 优惠券详情

**API对接：**
- `GET /api/v1/promotions/applicable` - 适用促销
- `POST /api/v1/promotions/coupons/validate` - 优惠券验证
- `GET /api/v1/promotions/coupons/{code}` - 优惠券详情

### 第三阶段：API接口适配优化（2天）

#### 任务3.1：错误处理机制统一（1天）
**文件：** `app/services/agent_tools.py`

**具体任务：**
1. 统一API错误响应格式
2. 添加重试机制（网络超时、服务暂不可用）
3. 实现优雅降级策略
4. 添加详细的错误日志记录

**预期输出：**
```python
class APIError(Exception):
    def __init__(self, message: str, status_code: int, details: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.details = details

def _call_api_with_retry(method: str, endpoint: str, max_retries: int = 3, **kwargs):
    # 实现重试逻辑
```

#### 任务3.2：响应数据格式优化（1天）
**文件：** `app/services/agent_tools.py`

**具体任务：**
1. 统一工具函数返回格式
2. 优化数据结构，便于AI理解
3. 添加数据验证和清洗
4. 实现响应数据缓存机制

### 第四阶段：客服场景集成测试（1天）

#### 任务4.1：端到端场景测试
**文件：** `tests/test_customer_service_scenarios.py`

**测试场景：**
1. **产品咨询场景**
   - 用户询问产品信息
   - 查询库存状态
   - 产品比较咨询

2. **促销查询场景**
   - 查询当前促销活动
   - 个人优惠券查询
   - 优惠券使用咨询

3. **订单跟踪场景**
   - 订单状态查询
   - 物流跟踪查询
   - 订单修改/取消

4. **售后服务场景**
   - 退换货资格查询
   - 退换货申请提交
   - 投诉建议提交

## 技术实施细节

### 用户上下文传递方案

```python
# 在ChatService中传递用户上下文
class ChatService:
    def __init__(self):
        self.current_user_context = None
    
    async def chat_stream(self, messages, user_token=None, **kwargs):
        # 验证用户Token并创建上下文
        if user_token:
            user_info = await self._verify_token(user_token)
            self.current_user_context = UserContext(
                user_id=user_info['id'],
                token=user_token,
                user_info=user_info
            )
        
        # 创建带有用户上下文的工具函数
        tools = self._create_tools_with_context(self.current_user_context)
        
        # 创建Agent
        agent = AssistantAgent(
            name=f"agent_{user_id}",
            model_client=self.model_client,
            tools=tools,
            # ... 其他配置
        )
```

### API认证集成方案

```python
# 工具函数认证机制
def _call_api(method: str, endpoint: str, user_context: Optional[UserContext] = None, **kwargs):
    headers = {"Content-Type": "application/json"}
    
    # 添加认证头
    if user_context and user_context.token:
        headers["Authorization"] = f"Bearer {user_context.token}"
    
    # API调用
    response = requests.request(method, url, headers=headers, **kwargs)
    
    # 处理认证错误
    if response.status_code == 401:
        return "用户认证失败，请重新登录"
    elif response.status_code == 403:
        return "权限不足，无法访问此资源"
    
    return response.json()
```

### 错误处理策略

```python
# 统一错误处理
def handle_api_error(response, endpoint):
    if response.status_code == 404:
        return "请求的资源不存在"
    elif response.status_code == 500:
        return "服务器内部错误，请稍后重试"
    elif response.status_code == 503:
        return "服务暂时不可用，请稍后重试"
    else:
        return f"请求失败：{response.text}"
```

## 质量保证

### 代码质量标准
1. **代码规范**：遵循PEP 8标准
2. **类型注解**：所有函数添加类型注解
3. **文档字符串**：详细的函数和类文档
4. **单元测试**：测试覆盖率 > 80%

### 测试策略
1. **单元测试**：每个工具函数独立测试
2. **集成测试**：API接口调用测试
3. **场景测试**：完整客服流程测试
4. **性能测试**：响应时间和并发测试

### 代码审查
1. **功能审查**：确保功能实现正确
2. **安全审查**：检查认证和权限控制
3. **性能审查**：优化API调用和内存使用
4. **可维护性审查**：代码结构和文档完整性

## 风险评估与应对

### 技术风险
1. **API兼容性问题**
   - 风险：现有API接口可能不完全满足需求
   - 应对：提前进行API接口测试，必要时协调后端API调整

2. **用户认证集成复杂度**
   - 风险：JWT Token集成可能遇到技术难题
   - 应对：参考现有认证机制，寻求技术支持

3. **性能瓶颈**
   - 风险：大量API调用可能影响响应速度
   - 应对：实现缓存机制，优化API调用策略

### 业务风险
1. **用户体验下降**
   - 风险：集成过程中可能出现功能不稳定
   - 应对：充分测试，分阶段发布

2. **数据安全问题**
   - 风险：用户数据传递过程中的安全隐患
   - 应对：严格遵循安全规范，加密敏感数据

## 成功标准

### 功能完成度
- [ ] 用户身份认证集成完成
- [ ] 12个核心工具函数实现并测试通过
- [ ] 6大客服场景基本功能可用
- [ ] API错误处理机制完善

### 性能指标
- [ ] 单次对话响应时间 < 3秒
- [ ] API调用成功率 > 95%
- [ ] 系统稳定性：连续运行24小时无崩溃
- [ ] 并发支持：同时支持10个用户对话

### 质量指标
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过率 100%
- [ ] 代码审查通过
- [ ] 文档完整性检查通过

## 后续计划

### 第二期功能增强（预计2周）
1. 个性化推荐算法集成
2. 智能问答优化
3. 多轮对话上下文管理
4. 用户画像分析

### 第三期高级功能（预计3周）
1. 人工转接机制
2. 多模态支持（图片、语音）
3. 数据分析和监控
4. 客服质量评估

## 项目团队与职责

### 开发团队
- **后端开发**：负责API接口开发和智能体集成
- **测试工程师**：负责功能测试和性能测试
- **产品经理**：负责需求确认和验收标准

### 沟通机制
- **日常沟通**：每日站会，同步进度和问题
- **周报告**：每周五提交进度报告
- **里程碑评审**：每个阶段完成后进行评审

---

**文档版本：** v1.0  
**创建日期：** 2024年12月  
**最后更新：** 2024年12月  
**负责人：** 待定  
**审核人：** 待定
