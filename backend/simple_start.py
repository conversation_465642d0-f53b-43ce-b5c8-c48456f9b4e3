"""
简化的启动服务器脚本（不使用Socket.IO）
"""

import uvicorn
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(__file__))

from app.core.config import settings

# 创建简化的FastAPI应用
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(
    title=settings.PROJECT_NAME,
    description="企业级AI智能体平台，提供智能客服、数据分析、知识库问答、文案创作等服务",
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 导入API路由
from app.api.v1.api import api_router
app.include_router(api_router, prefix=settings.API_V1_STR)

# 健康检查
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.VERSION}

@app.get("/")
async def root():
    return {
        "message": "YUE智能体综合应用平台",
        "version": settings.VERSION,
        "docs": "/docs",
        "health": "/health"
    }

if __name__ == "__main__":
    print("🚀 启动YUE智能体综合应用平台后端服务...")
    print(f"📍 服务地址: http://{settings.HOST}:{settings.PORT}")
    print(f"📖 API文档: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"🔧 调试模式: {'开启' if settings.DEBUG else '关闭'}")
    
    uvicorn.run(
        "simple_start:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
