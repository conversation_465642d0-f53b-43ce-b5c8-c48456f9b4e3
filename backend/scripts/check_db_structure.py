"""
检查数据库结构脚本
"""

import pymysql
import sys
import os

# 添加项目根目录到Python路径
# 获取当前脚本的目录 (backend/scripts)
current_dir = os.path.dirname(__file__)
# 获取backend目录
backend_dir = os.path.dirname(current_dir)
# 添加backend目录到Python路径，这样可以导入app模块
sys.path.insert(0, backend_dir)

from app.core.config import settings

def check_database_structure():
    """检查数据库结构"""
    try:
        # 连接到MySQL数据库
        connection = pymysql.connect(
            host=settings.MYSQL_SERVER,
            port=settings.MYSQL_PORT,
            user=settings.MYSQL_USER,
            password=settings.MYSQL_PASSWORD,
            database=settings.MYSQL_DB,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查所有表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            
            print("📋 数据库表列表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            print("\n🔍 检查外键约束:")
            # 检查外键约束
            cursor.execute("""
                SELECT 
                    TABLE_NAME,
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM 
                    INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE 
                    REFERENCED_TABLE_SCHEMA = %s
                    AND REFERENCED_TABLE_NAME IS NOT NULL
            """, (settings.MYSQL_DB,))
            
            foreign_keys = cursor.fetchall()
            
            if foreign_keys:
                print("❌ 发现以下外键约束:")
                for fk in foreign_keys:
                    print(f"  - {fk[0]}.{fk[1]} -> {fk[3]}.{fk[4]} (约束名: {fk[2]})")
            else:
                print("✅ 没有发现显式外键约束！")
            
            print("\n📊 检查几个关键表的结构:")
            
            # 检查几个关键表的结构
            key_tables = ['products', 'orders', 'order_items', 'user_coupons']
            
            for table_name in key_tables:
                print(f"\n📋 {table_name} 表结构:")
                cursor.execute(f"DESCRIBE {table_name}")
                columns = cursor.fetchall()
                
                for column in columns:
                    field_name = column[0]
                    field_type = column[1]
                    is_null = column[2]
                    key_info = column[3]
                    default_val = column[4]
                    extra = column[5]
                    
                    print(f"  - {field_name}: {field_type} {'NULL' if is_null == 'YES' else 'NOT NULL'} {key_info} {extra}")
        
        connection.close()
        print("\n✅ 数据库结构检查完成！")
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
        raise

if __name__ == "__main__":
    check_database_structure()
