{"name": "yue-ai-agent-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "react-syntax-highlighter": "^15.5.0", "@headlessui/react": "^1.7.17"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}