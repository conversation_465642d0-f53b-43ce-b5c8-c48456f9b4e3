# YUE智能体综合应用平台

## 项目简介

YUE智能体综合应用平台是一个企业级的AI智能体平台，提供多种智能体服务：

- 🤖 智能客服智能体
- 📊 Text2SQL数据分析智能体  
- 📚 企业级知识库问答智能体
- ✍️ 企业内部文案创作智能体

## 技术架构

### 前端技术栈
- React 18 + TypeScript
- Vite (构建工具)
- Tailwind CSS + Framer Motion
- Zustand (状态管理)
- React Query (数据获取)
- Socket.io (实时通信)

### 后端技术栈
- Python 3.11+ + FastAPI
- SQLAlchemy + Alembic
- MySQL (关系数据库)
- Milvus (向量数据库)
- MinIO (文件存储)
- Redis (缓存)
- Celery (异步任务)

## 系统架构

```mermaid
graph TB
    A[前端 React+TS] --> B[API网关 FastAPI]
    B --> C[智能体服务层]
    C --> D1[MySQL]
    C --> D2[Milvu<PERSON>]
    C --> D3[Min<PERSON>]
    C --> D4[Redis]
    C --> E[AI服务 OpenAI+LangChain]

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style E fill:#fce4ec
```

> 📋 详细架构说明请查看 [系统架构文档](docs/architecture.md)

## 项目结构

```
yue_ai_agent/
├── frontend/                 # 前端项目
├── backend/                  # 后端项目
├── docker/                   # Docker配置
├── docs/                     # 项目文档
├── scripts/                  # 部署脚本
└── README.md
```

## 快速开始

### 环境要求
- Node.js 18+
- Python 3.11+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 6.0+

### 安装依赖

#### 前端
```bash
cd frontend
npm install
```

#### 后端
```bash
cd backend
pip install -r requirements.txt
```

### 启动服务

#### 使用Docker Compose (推荐)
```bash
docker-compose up -d
```

#### 手动启动
```bash
# 启动后端
cd backend
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 启动前端
cd frontend
npm run dev
```

## 开发指南

详细的开发指南请参考 [docs/development.md](docs/development.md)

## 部署指南

详细的部署指南请参考 [docs/deployment.md](docs/deployment.md)

## 许可证

MIT License
